<mujoco model="engineai_robotics">
    <compiler angle="radian" discardvisual="false" autolimits="true" eulerseq="zyx"/>
    <option timestep="0.002" tolerance="1e-10"/>

    <default>
        <default class="biped">
            <default class="collision">
                <geom group="3" contype="1" conaffinity="1" friction="0.8 0.005 0.0001" rgba="0.804 0.361 0.031 0.5"/>
                <default class="collision_head">
                    <geom type="sphere" size="0.09" pos="0.01 0.0 0.11" rgba="0.804 0.361 0.031 0.5"/>
                </default>

                <default class="collision_base_upper_box">
                    <geom type="box" size="0.085 0.096 0.135" pos="-0.008 0.0 0.15" rgba="0.804 0.361 0.031 0.5"/>
                    <!-- If accurate torso model needed, use original mesh as follows -->
                    <!-- <geom type="mesh" mesh="LINK_TORSO_YAW" rgba="0.804 0.361 0.031 0.5"/> -->
                </default>

                <default class="collision_base_lower_box">
                    <geom type="sphere" size="0.085" pos="0.02 0.0 -0.02" rgba="0.804 0.361 0.031 0.5"/>
                </default>

                <default class="collision_left_foot">
                    <geom type="box" size="0.102292353 0.053555 0.01" pos="0.0170 0.00166 -0.03631" rgba="0.804 0.361 0.031 0.5"/>
                </default>

                <default class="collision_left_foot_toe">
                    <geom type="box" size="0.028 0.047805 0.01" quat="0.9741537 0 -0.2258863 0" pos="0.139 0.0 -0.0235" rgba="0.804 0.361 0.031 0.5"/>
                </default>

                <default class="collision_right_foot">
                    <geom type="box" size="0.102292353 0.053555 0.01" pos="0.0170 -0.00166 -0.03631" rgba="0.804 0.361 0.031 0.5"/>
                </default>

                <default class="collision_right_foot_toe">
                    <geom type="box" size="0.028 0.047805 0.01" quat="0.9741537 0 -0.2258863 0" pos="0.139 0.0 -0.0235" rgba="0.804 0.361 0.031 0.5"/>
                </default>

                <default class="collision_left_elbow_end">
                    <geom type="sphere" size="0.05" pos="0.03 -0.02 -0.14" rgba="0.804 0.361 0.031 0.5" />
                </default>
                <default class="collision_elbow">
                    <geom type="sphere" size="0.045" pos="0.0 0.0 -0.0" rgba="0.804 0.361 0.031 0.5"/>
                </default>

                <default class="collision_right_elbow_end">
                    <geom type="sphere" size="0.05" pos="0.03 0.02 -0.14" rgba="0.804 0.361 0.031 0.5"/>
                </default>

                <default class="collision_hip_left">
                    <geom type="cylinder" size="0.046 0.06" pos="0.007 0.05 -0.012" quat="1 0 1 0" rgba="0.804 0.361 0.031 0.5"/>
                </default>

                <default class="collision_hip_right">
                    <geom type="cylinder" size="0.046 0.06" pos="0.007 -0.05 -0.012" quat="1 0 1 0" rgba="0.804 0.361 0.031 0.5"/>
                </default>

                <default class="collision_knee">
                    <geom type="sphere" size="0.062" pos="0.004 0.00 -0.0" rgba="0.804 0.361 0.031 0.5"/>
                </default>
            </default>

            <default class="visual">
                <geom contype="0" conaffinity="0" group="2"/>
            </default>

            <default class="joint">
                <joint damping="0.6" frictionloss="0.8"/>
            </default>

        </default>
    </default>

    <asset>
        <include file="assets.xml"/>
    </asset>

    <worldbody>
        <light name="spotlight" mode="targetbodycom" target="LINK_BASE" pos="0 -1 2"/>
        <body name="LINK_BASE" pos="0 0 0.82">
            <inertial pos="0.01525750 -0.00001621 -0.02333111" mass="4.08640035" fullinertia="0.02021593 0.01531282 0.01216285 -0.00000120 -0.00037784 -0.00000295"/>
            <geom type="mesh" rgba="0.75294 0.75294 0.75294 1" mesh="LINK_BASE" class="visual"/>
            <geom class="collision_base_lower_box"/>
            <site name="base" pos="0 0 0"/>
            <site name="imu" pos="0.02311 0 -0.09631"/>
            <freejoint/>

            <include file="serial_links.xml"/>
        </body>
    </worldbody>

    <include file="serial_actuators.xml"/>
    <include file="serial_sensors.xml"/>

    <!-- Comments this keyframe when fixed base used -->
    <keyframe>
        <key name="floating_base_homing" qpos="0 0 0.82 1 0 0 0 0 0 -0.12 0.24 -0.12 0 0 0 -0.12 0.24 -0.12 0 0 0 0 0 0 0 0 0 0 0 0 0"/>
    </keyframe>

</mujoco>
