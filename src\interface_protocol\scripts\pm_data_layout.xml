<?xml version='1.0' encoding='UTF-8'?>
<root>
 <tabbed_widget parent="main_window" name="Main Window">
  <Tab tab_name="imu" containers="1">
   <Container>
    <DockSplitter sizes="1" count="1" orientation="-">
     <DockSplitter sizes="0.333487;0.333025;0.333487" count="3" orientation="|">
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532269" top="0.022935" right="51.530526" bottom="-0.022743"/>
         <limitY/>
         <curve color="#1f77b4" name="/hardware/imu_info/rpy/x"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532269" top="0.674534" right="51.530526" bottom="-0.721598"/>
         <limitY/>
         <curve color="#ff7f0e" name="/hardware/imu_info/angular_velocity/x"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532269" top="0.026076" right="51.530526" bottom="-0.005975"/>
         <limitY/>
         <curve color="#d62728" name="/hardware/imu_info/rpy/y"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532269" top="0.207022" right="51.530526" bottom="-0.294063"/>
         <limitY/>
         <curve color="#f14cc1" name="/hardware/imu_info/angular_velocity/y"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532269" top="-0.112675" right="51.530526" bottom="-0.123844"/>
         <limitY/>
         <curve color="#1ac938" name="/hardware/imu_info/rpy/z"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532269" top="0.182792" right="51.530526" bottom="-0.398654"/>
         <limitY/>
         <curve color="#9467bd" name="/hardware/imu_info/angular_velocity/z"/>
        </plot>
       </DockArea>
      </DockSplitter>
     </DockSplitter>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab tab_name="leg joint pos" containers="1">
   <Container>
    <DockSplitter sizes="1" count="1" orientation="-">
     <DockSplitter sizes="0.333487;0.333025;0.333487" count="3" orientation="|">
      <DockSplitter sizes="0.25;0.25;0.25;0.25" count="4" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.132317" right="51.530546" bottom="-0.638259"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[0]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[0]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="1.246336" right="51.530546" bottom="-0.452308"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[3]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[3]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.011538" right="51.530546" bottom="-0.579512"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[6]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[6]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="1.061470" right="51.530546" bottom="-0.154423"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[9]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[9]"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.25;0.25;0.25;0.25" count="4" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="1.266498" right="51.530546" bottom="-0.461232"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[1]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[1]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="1.446081" right="51.530546" bottom="-0.628373"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[4]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[4]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.683812" right="51.530546" bottom="-1.016646"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[7]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[7]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="1.359056" right="51.530546" bottom="-0.563011"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[10]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[10]"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.25;0.25;0.25;0.25" count="4" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.381706" right="51.530546" bottom="-0.430956"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[2]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[2]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.552013" right="51.530546" bottom="-0.181586"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[5]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[5]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.246894" right="51.530546" bottom="-0.438654"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[8]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[8]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.303907" right="51.530546" bottom="-0.664530"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[11]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[11]"/>
        </plot>
       </DockArea>
      </DockSplitter>
     </DockSplitter>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab tab_name="arm joint pos" containers="1">
   <Container>
    <DockSplitter sizes="1" count="1" orientation="-">
     <DockSplitter sizes="0.200185;0.199723;0.200185;0.199723;0.200185" count="5" orientation="|">
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.007653" right="51.530546" bottom="-0.003838"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[13]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[13]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.002522" right="51.530546" bottom="-0.000062"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[18]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[18]"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.001275" right="51.530546" bottom="-0.007581"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[14]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[14]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.007589" right="51.530546" bottom="-0.002610"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[19]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[19]"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.000031" right="51.530546" bottom="-0.000126"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[15]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[15]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.000115" right="51.530546" bottom="-0.000049"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[20]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[20]"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.001204" right="51.530546" bottom="-0.000029"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[16]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[16]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.000911" right="51.530546" bottom="-0.000022"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[21]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[21]"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.000001" right="51.530546" bottom="-0.000031"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[17]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[17]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.000027" right="51.530546" bottom="-0.000001"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/position[22]"/>
         <curve color="#d62728" name="/hardware/joint_command/position[22]"/>
        </plot>
       </DockArea>
      </DockSplitter>
     </DockSplitter>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab tab_name="waist and head" containers="1">
   <Container>
    <DockSplitter sizes="1" count="1" orientation="-">
     <DockSplitter sizes="0.5;0.5" count="2" orientation="|">
      <DockArea name="...">
       <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
        <range left="46.532275" top="0.007412" right="51.530546" bottom="-0.005961"/>
        <limitY/>
        <curve color="#17becf" name="/hardware/joint_state/position[12]"/>
        <curve color="#d62728" name="/hardware/joint_command/position[12]"/>
       </plot>
      </DockArea>
      <DockArea name="...">
       <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
        <range left="46.532275" top="0.000041" right="51.530546" bottom="-0.000040"/>
        <limitY/>
        <curve color="#17becf" name="/hardware/joint_state/position[23]"/>
        <curve color="#d62728" name="/hardware/joint_command/position[23]"/>
       </plot>
      </DockArea>
     </DockSplitter>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab tab_name="leg joint torque" containers="1">
   <Container>
    <DockSplitter sizes="1" count="1" orientation="-">
     <DockSplitter sizes="0.333487;0.333025;0.333487" count="3" orientation="|">
      <DockSplitter sizes="0.25;0.25;0.25;0.25" count="4" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="22.783771" right="51.530546" bottom="-28.270860"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/torque[0]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="47.699573" right="51.530546" bottom="-43.708123"/>
         <limitY/>
         <curve color="#d62728" name="/hardware/joint_state/torque[3]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="24.139127" right="51.530546" bottom="-25.601629"/>
         <limitY/>
         <curve color="#f14cc1" name="/hardware/joint_state/torque[6]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="31.455225" right="51.530546" bottom="-47.377484"/>
         <limitY/>
         <curve color="#bcbd22" name="/hardware/joint_state/torque[9]"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.25;0.25;0.25;0.25" count="4" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="63.800826" right="51.530546" bottom="-24.015794"/>
         <limitY/>
         <curve color="#bcbd22" name="/hardware/joint_state/torque[1]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="34.425731" right="51.530546" bottom="-3.209418"/>
         <limitY/>
         <curve color="#1ac938" name="/hardware/joint_state/torque[4]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="34.380906" right="51.530546" bottom="-51.559169"/>
         <limitY/>
         <curve color="#9467bd" name="/hardware/joint_state/torque[7]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="32.086888" right="51.530546" bottom="-3.890172"/>
         <limitY/>
         <curve color="#1f77b4" name="/hardware/joint_state/torque[10]"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.25;0.25;0.25;0.25" count="4" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="15.066882" right="51.530546" bottom="-20.995955"/>
         <limitY/>
         <curve color="#1f77b4" name="/hardware/joint_state/torque[2]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="10.716545" right="51.530546" bottom="-4.118482"/>
         <limitY/>
         <curve color="#ff7f0e" name="/hardware/joint_state/torque[5]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="14.994088" right="51.530546" bottom="-20.987145"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/torque[8]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="5.903207" right="51.530546" bottom="-12.970143"/>
         <limitY/>
         <curve color="#d62728" name="/hardware/joint_state/torque[11]"/>
        </plot>
       </DockArea>
      </DockSplitter>
     </DockSplitter>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab tab_name="arm joint torque" containers="1">
   <Container>
    <DockSplitter sizes="1" count="1" orientation="-">
     <DockSplitter sizes="0.200185;0.199723;0.200185;0.199723;0.200185" count="5" orientation="|">
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.962737" right="51.530546" bottom="-1.928057"/>
         <limitY/>
         <curve color="#1ac938" name="/hardware/joint_state/torque[13]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.003940" right="51.530546" bottom="-0.632645"/>
         <limitY/>
         <curve color="#17becf" name="/hardware/joint_state/torque[18]"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="1.896273" right="51.530546" bottom="-0.319109"/>
         <limitY/>
         <curve color="#ff7f0e" name="/hardware/joint_state/torque[14]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.654003" right="51.530546" bottom="-1.897423"/>
         <limitY/>
         <curve color="#bcbd22" name="/hardware/joint_state/torque[19]"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="1.896273" right="51.530546" bottom="-0.319109"/>
         <limitY/>
         <curve color="#ff7f0e" name="/hardware/joint_state/torque[14]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.012419" right="51.530546" bottom="-0.028753"/>
         <limitY/>
         <curve color="#1f77b4" name="/hardware/joint_state/torque[20]"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="-0.086444" right="51.530546" bottom="-0.299634"/>
         <limitY/>
         <curve color="#f14cc1" name="/hardware/joint_state/torque[16]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="-0.114062" right="51.530546" bottom="-0.225635"/>
         <limitY/>
         <curve color="#d62728" name="/hardware/joint_state/torque[21]"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter sizes="0.5;0.5" count="2" orientation="-">
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="0.007640" right="51.530546" bottom="0.003524"/>
         <limitY/>
         <curve color="#9467bd" name="/hardware/joint_state/torque[17]"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot flip_y="false" mode="TimeSeries" style="Lines" flip_x="false">
         <range left="46.532275" top="-0.003772" right="51.530546" bottom="-0.006690"/>
         <limitY/>
         <curve color="#1ac938" name="/hardware/joint_state/torque[22]"/>
        </plot>
       </DockArea>
      </DockSplitter>
     </DockSplitter>
    </DockSplitter>
   </Container>
  </Tab>
  <currentTabIndex index="1"/>
 </tabbed_widget>
 <use_relative_time_offset enabled="1"/>
 <!-- - - - - - - - - - - - - - - -->
 <!-- - - - - - - - - - - - - - - -->
 <Plugins>
  <plugin ID="DataLoad CSV">
   <parameters delimiter="0" time_axis=""/>
  </plugin>
  <plugin ID="DataLoad MCAP"/>
  <plugin ID="DataLoad ROS2 bags">
   <use_header_stamp value="false"/>
   <discard_large_arrays value="true"/>
   <max_array_size value="100"/>
   <boolean_strings_to_number value="true"/>
   <remove_suffix_from_strings value="true"/>
   <selected_topics value=""/>
  </plugin>
  <plugin ID="DataLoad ULog"/>
  <plugin ID="ROS2 Topic Subscriber">
   <use_header_stamp value="false"/>
   <discard_large_arrays value="true"/>
   <max_array_size value="100"/>
   <boolean_strings_to_number value="true"/>
   <remove_suffix_from_strings value="true"/>
   <selected_topics value="/hardware/imu_info;/hardware/joint_command;/hardware/joint_state"/>
  </plugin>
  <plugin ID="UDP Server"/>
  <plugin ID="WebSocket Server"/>
  <plugin ID="ZMQ Subscriber"/>
  <plugin ID="Fast Fourier Transform"/>
  <plugin ID="Quaternion to RPY"/>
  <plugin ID="Reactive Script Editor">
   <library code="--[[ Helper function to create a series from arrays&#xa;&#xa; new_series: a series previously created with ScatterXY.new(name)&#xa; prefix:     prefix of the timeseries, before the index of the array&#xa; suffix_X:   suffix to complete the name of the series containing the X value. If [nil], use the index of the array.&#xa; suffix_Y:   suffix to complete the name of the series containing the Y value&#xa; timestamp:   usually the tracker_time variable&#xa;              &#xa; Example:&#xa; &#xa; Assuming we have multiple series in the form:&#xa; &#xa;   /trajectory/node.{X}/position/x&#xa;   /trajectory/node.{X}/position/y&#xa;   &#xa; where {N} is the index of the array (integer). We can create a reactive series from the array with:&#xa; &#xa;   new_series = ScatterXY.new(&quot;my_trajectory&quot;) &#xa;   CreateSeriesFromArray( new_series, &quot;/trajectory/node&quot;, &quot;position/x&quot;, &quot;position/y&quot;, tracker_time );&#xa;--]]&#xa;&#xa;function CreateSeriesFromArray( new_series, prefix, suffix_X, suffix_Y, timestamp )&#xa;  &#xa;  --- clear previous values&#xa;  new_series:clear()&#xa;  &#xa;  --- Append points to new_series&#xa;  index = 0&#xa;  while(true) do&#xa;&#xa;    x = index;&#xa;    -- if not nil, get the X coordinate from a series&#xa;    if suffix_X ~= nil then &#xa;      series_x = TimeseriesView.find( string.format( &quot;%s.%d/%s&quot;, prefix, index, suffix_X) )&#xa;      if series_x == nil then break end&#xa;      x = series_x:atTime(timestamp)&#x9; &#xa;    end&#xa;    &#xa;    series_y = TimeseriesView.find( string.format( &quot;%s.%d/%s&quot;, prefix, index, suffix_Y) )&#xa;    if series_y == nil then break end &#xa;    y = series_y:atTime(timestamp)&#xa;    &#xa;    new_series:push_back(x,y)&#xa;    index = index+1&#xa;  end&#xa;end&#xa;&#xa;--[[ Similar to the built-in function GetSeriesNames(), but select only the names with a give prefix. --]]&#xa;&#xa;function GetSeriesNamesByPrefix(prefix)&#xa;  -- GetSeriesNames(9 is a built-in function&#xa;  all_names = GetSeriesNames()&#xa;  filtered_names = {}&#xa;  for i, name in ipairs(all_names)  do&#xa;    -- check the prefix&#xa;    if name:find(prefix, 1, #prefix) then&#xa;      table.insert(filtered_names, name);&#xa;    end&#xa;  end&#xa;  return filtered_names&#xa;end&#xa;&#xa;--[[ Modify an existing series, applying offsets to all their X and Y values&#xa;&#xa; series: an existing timeseries, obtained with TimeseriesView.find(name)&#xa; delta_x: offset to apply to each x value&#xa; delta_y: offset to apply to each y value &#xa;  &#xa;--]]&#xa;&#xa;function ApplyOffsetInPlace(series, delta_x, delta_y)&#xa;  -- use C++ indeces, not Lua indeces&#xa;  for index=0, series:size()-1 do&#xa;    x,y = series:at(index)&#xa;    series:set(index, x + delta_x, y + delta_y)&#xa;  end&#xa;end&#xa;"/>
   <scripts/>
  </plugin>
  <plugin ID="CSV Exporter"/>
  <plugin ID="ROS2 Topic Re-Publisher"/>
 </Plugins>
 <!-- - - - - - - - - - - - - - - -->
 <previouslyLoaded_Datafiles/>
 <previouslyLoaded_Streamer name="ROS2 Topic Subscriber"/>
 <!-- - - - - - - - - - - - - - - -->
 <customMathEquations/>
 <snippets/>
 <!-- - - - - - - - - - - - - - - -->
</root>

