kp:
  - [100, 100, 100, 100, 100, 100]
  - [100, 100, 100, 100, 100, 100]
  - [100]
  - [100, 100, 100, 100, 100]
  - [100, 100, 100, 100, 100]
  - [100]
kd:
  - [1, 1, 1, 1, 1, 1]
  - [1, 1, 1, 1, 1, 1]
  - [1]
  - [1, 1, 1, 1, 1]
  - [1, 1, 1, 1, 1]
  - [1]

# Target position
target_position:
  - [0.0, 0.5, 1.57, 0.6, -0.3, 0]
  - [-0.0, -0.5, -1.57, 0.6, -0.3, 0]
  - [0]
  - [0, 0.3, -0, -0.4, 0]
  - [0, -0.2, -0, -0.3, 0]
  - [0]

# Total steps
num_steps:
  - [2000, 2000, 2000, 2000, 2000, 2000]
  - [2000, 2000, 2000, 2000, 2000, 2000]
  - [2000]
  - [2000, 2000, 2000, 2000, 2000]
  - [2000, 2000, 2000, 2000, 2000]
  - [2000]
