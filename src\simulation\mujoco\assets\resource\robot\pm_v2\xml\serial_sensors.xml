<mujoco model = "serial_sensor">

    <!-- !!! Do not change the order of these sensors unless you want to modify the mujuco `main.cc` -->
    <sensor>
        <!-- Sets base link states to the base site -->
        <framequat name="base_link_quaternion" objtype="site" objname="base"/>
        <framepos name="base_link_position" objtype="site" objname="base"/>
        <framelinvel name="base_link_linear_velocity" objtype="site" objname="base"/>
        <frameangvel name="base_link_angular_velocity" objtype="site" objname="base"/>

        <!-- Sets imu link states to the imu site -->
        <framequat name="imu_link_quaternion" objtype="site" objname="imu"/>
        <framepos name="imu_link_position" objtype="site" objname="imu"/>
        <framelinvel name="imu_link_linear_velocity" objtype="site" objname="imu"/>
        <frameangvel name="imu_link_angular_velocity" objtype="site" objname="imu"/>

        <!-- Sets imu states to the imu site -->
        <framequat name="imu_quaternion" objtype="site" objname="imu"/>
        <accelerometer name="imu_linear_acceleration" noise="0" cutoff="0" site="imu"/>
        <gyro name="imu_angular_velocity" noise="0" cutoff="0" site="imu"/>

        <!-- Sets force/torque states to the force/torque sensor site -->
        <force name="force_left_foot" site="force_sensor_left_foot"/>
        <torque name="torque_left_foot" site="torque_sensor_left_foot"/>
        <force name="force_right_foot" site="force_sensor_right_foot"/>
        <torque name="torque_right_foot" site="torque_sensor_right_foot"/>
    </sensor>

</mujoco>