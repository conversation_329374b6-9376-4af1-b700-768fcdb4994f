<mujoco model="ground">

  <statistic center="0 0 0.5" extent="1"/>

  <visual>
    <headlight diffuse="0.6 0.6 0.6" ambient="0.3 0.3 0.3" specular="0 0 0"/>
    <rgba haze="0.15 0.25 0.35 1"/>
    <global azimuth="150" elevation="-20"/>
    <quality shadowsize="0"/>
  </visual>

  <visual>
    <rgba com="1 0.84 0 0.5" contactforce="0.8 0.36 0.36 1" contactpoint="1.0 1.0 0.6 0.4"/>
    <scale com="1" forcewidth="0.02" contactwidth="0.01" contactheight="0.02" framewidth="0.05" framelength="0.6"/>
    <map force="0.01"/>
  </visual>

  <asset>
    <!-- <texture name="skybox" type="skybox" builtin="gradient" rgb1="0.1 0.1 0.1" rgb2="0 0 0" width="1000" height="1000" mark="random" random="0.001" markrgb="1 1 1"/> -->
    <texture type="2d" name="groundplane" builtin="checker" mark="edge" rgb1="0.2 0.3 0.4" rgb2="0.1 0.2 0.3" markrgb="0.8 0.8 0.8" width="1000" height="1000"/>
    <material name="groundplane" texture="groundplane" texuniform="true" texrepeat="5 5" reflectance="0"/>
  </asset>

  <worldbody>
    <light pos="0 0 10" dir="0 0 -1" directional="true"/>
    <geom name="floor" size="0 5 0.125" type="plane" material="groundplane" conaffinity="7" condim="3" friction="1"/>
  </worldbody>

</mujoco>
